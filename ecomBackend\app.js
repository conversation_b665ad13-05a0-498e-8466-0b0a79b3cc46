const express = require('express');
const dotenv = require('dotenv');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const morgan = require('morgan');

// Import routes
const authRoutes = require('./routes/auth.routes');
const cartRoutes = require('./routes/cart.routes');
const orderRoutes = require('./routes/order.routes');
const productRoutes = require('./routes/product.routes');
const userRoutes = require('./routes/user.routes');
const adminRoutes = require('./routes/admin.routes');
const sellerRoutes = require('./routes/seller.routes');
const vendorRoutes = require('./routes/vendor.routes');
const deliveryRoutes = require('./routes/delivery.routes');
const categoryRoutes = require('./routes/category.routes');
const paymentRoutes = require('./routes/payments.routes');
const couponRoutes = require('./routes/coupon.routes');
const notificationRoutes = require('./routes/notification.routes');
const carouselRoutes = require('./routes/carousel.routes');
const offerRoutes = require('./routes/offer.routes');

// Import the database connection configuration
const dbConnection = require('./config/db.config');

// Load environment variables from .env file
dotenv.config();

const app = express();

// Middleware setup
app.use(helmet());
app.use(express.json());
app.use(cors({
  origin: process.env.CLIENT_URL,
  credentials: true,
}));

// Rate Limiting Middleware
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 100,
  message: 'Too many requests, please try again later.',
});
app.use(limiter);

app.use(morgan('combined'));


// Connect to the database
dbConnection();

// Routes setup
app.use('/api/auth', authRoutes);
app.use('/api/cart', cartRoutes);
app.use('/api/orders', orderRoutes);
app.use('/api/products', productRoutes);
app.use('/api/users', userRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/seller', sellerRoutes);
app.use('/api/vendor', vendorRoutes);
app.use('/api/delivery', deliveryRoutes);
app.use('/api/categories', categoryRoutes);
app.use('/api/payment', paymentRoutes);
app.use('/api/coupons', couponRoutes);
app.use('/api/notifications', notificationRoutes);
app.use('/api/carousel', carouselRoutes);
app.use('/api/offers', offerRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'OK', message: 'Server is running' });
});

// Error handling middleware for 404 routes
app.use((req, res, next) => {
  res.status(404).json({ message: 'Route not found' });
});

// General error handling middleware
app.use((err, req, res, next) => {
  // Log the error with stack trace for debugging
  console.error('Error:', err.message);
  console.error('Stack:', err.stack);

  // Set appropriate status code
  const statusCode = err.statusCode || 500;

  // Prepare error response based on environment
  const errorResponse = {
    message: statusCode === 500 ? 'Internal Server Error' : err.message,
    error: process.env.NODE_ENV === 'development' ? err.stack : undefined,
    status: 'error',
    timestamp: new Date().toISOString(),
    path: req.originalUrl
  };

  // Send error response
  res.status(statusCode).json(errorResponse);
});


// Start the server
const PORT = process.env.PORT || 5200; // Changed from 5100 to 5200
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});

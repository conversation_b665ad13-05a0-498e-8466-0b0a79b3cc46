import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError, BehaviorSubject, of } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { environment } from '../../environments/environment';

// Interfaces
export interface CarouselSlide {
  id: string;
  _id: string;
  title: string;
  subtitle?: string;
  description?: string;
  image: {
    url: string;
    public_id: string;
  };
  buttonText?: string;
  buttonLink?: string;
  isActive: boolean;
  order: number;
  startDate?: string;
  endDate?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CarouselResponse {
  slides: CarouselSlide[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

@Injectable({
  providedIn: 'root'
})
export class CarouselService {
  private readonly apiUrl = `${environment.apiBaseUrl}/api/carousel`;

  // Cache carousel slides for better performance
  private slidesSubject = new BehaviorSubject<CarouselSlide[]>([]);
  public slides$ = this.slidesSubject.asObservable();

  private activeSlidesSubject = new BehaviorSubject<CarouselSlide[]>([]);
  public activeSlides$ = this.activeSlidesSubject.asObservable();

  constructor(private http: HttpClient) {
    // Load slides on service initialization
    this.loadSlides();
  }

  // ========== PUBLIC METHODS ==========

  /**
   * Get all carousel slides (Admin)
   */
  getAllSlides(page: number = 1, limit: number = 10): Observable<CarouselResponse> {
    return this.http.get<CarouselResponse>(`${this.apiUrl}/admin?page=${page}&limit=${limit}`)
      .pipe(
        tap(response => this.slidesSubject.next(response.slides || [])),
        catchError(this.handleError)
      );
  }

  /**
   * Get active carousel slides (Public)
   */
  getActiveSlides(): Observable<CarouselSlide[]> {
    return this.http.get<{ slides: CarouselSlide[] }>(`${this.apiUrl}/active`)
      .pipe(
        map(response => response.slides || []),
        tap(slides => this.activeSlidesSubject.next(slides)),
        catchError(error => {
          console.warn('Carousel API not available, using sample data');
          const sampleSlides = this.getSampleSlides();
          this.activeSlidesSubject.next(sampleSlides);
          return of(sampleSlides);
        })
      );
  }

  /**
   * Get slide by ID
   */
  getSlideById(slideId: string): Observable<CarouselSlide> {
    return this.http.get<{ slide: CarouselSlide }>(`${this.apiUrl}/${slideId}`)
      .pipe(
        map(response => response.slide),
        catchError(this.handleError)
      );
  }

  /**
   * Create new carousel slide (Admin)
   */
  createSlide(slideData: FormData): Observable<CarouselSlide> {
    return this.http.post<{ slide: CarouselSlide }>(`${this.apiUrl}`, slideData)
      .pipe(
        map(response => response.slide),
        tap(() => this.refreshSlides()),
        catchError(this.handleError)
      );
  }

  /**
   * Update carousel slide (Admin)
   */
  updateSlide(slideId: string, slideData: FormData): Observable<CarouselSlide> {
    return this.http.put<{ slide: CarouselSlide }>(`${this.apiUrl}/${slideId}`, slideData)
      .pipe(
        map(response => response.slide),
        tap(() => this.refreshSlides()),
        catchError(this.handleError)
      );
  }

  /**
   * Delete carousel slide (Admin)
   */
  deleteSlide(slideId: string): Observable<{ message: string }> {
    return this.http.delete<{ message: string }>(`${this.apiUrl}/${slideId}`)
      .pipe(
        tap(() => this.refreshSlides()),
        catchError(this.handleError)
      );
  }

  /**
   * Toggle slide status (Admin)
   */
  toggleSlideStatus(slideId: string, isActive: boolean): Observable<CarouselSlide> {
    return this.http.patch<{ slide: CarouselSlide }>(`${this.apiUrl}/${slideId}/status`, { isActive })
      .pipe(
        map(response => response.slide),
        tap(() => this.refreshSlides()),
        catchError(this.handleError)
      );
  }

  /**
   * Update slide order (Admin)
   */
  updateSlideOrder(slideId: string, order: number): Observable<CarouselSlide> {
    return this.http.patch<{ slide: CarouselSlide }>(`${this.apiUrl}/${slideId}/order`, { order })
      .pipe(
        map(response => response.slide),
        tap(() => this.refreshSlides()),
        catchError(this.handleError)
      );
  }

  /**
   * Bulk update slide orders (Admin)
   */
  updateSlidesOrder(slides: { id: string; order: number }[]): Observable<{ message: string }> {
    return this.http.patch<{ message: string }>(`${this.apiUrl}/bulk-order`, { slides })
      .pipe(
        tap(() => this.refreshSlides()),
        catchError(this.handleError)
      );
  }

  // ========== UTILITY METHODS ==========

  /**
   * Refresh slides cache
   */
  refreshSlides(): void {
    this.loadSlides();
  }

  /**
   * Get cached slides (synchronous)
   */
  getCachedSlides(): CarouselSlide[] {
    return this.slidesSubject.value;
  }

  /**
   * Get cached active slides (synchronous)
   */
  getCachedActiveSlides(): CarouselSlide[] {
    return this.activeSlidesSubject.value;
  }

  /**
   * Check if slide is currently active (within date range)
   */
  isSlideCurrentlyActive(slide: CarouselSlide): boolean {
    if (!slide.isActive) return false;

    const now = new Date();
    const startDate = slide.startDate ? new Date(slide.startDate) : null;
    const endDate = slide.endDate ? new Date(slide.endDate) : null;

    if (startDate && now < startDate) return false;
    if (endDate && now > endDate) return false;

    return true;
  }

  /**
   * Get slides sorted by order
   */
  getSlidesSortedByOrder(slides: CarouselSlide[]): CarouselSlide[] {
    return slides.sort((a, b) => a.order - b.order);
  }

  /**
   * Validate slide data
   */
  validateSlideData(slideData: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!slideData.title || slideData.title.trim().length === 0) {
      errors.push('Title is required');
    }

    if (slideData.title && slideData.title.length > 100) {
      errors.push('Title must be less than 100 characters');
    }

    if (slideData.description && slideData.description.length > 500) {
      errors.push('Description must be less than 500 characters');
    }

    if (slideData.buttonLink && !this.isValidUrl(slideData.buttonLink)) {
      errors.push('Button link must be a valid URL');
    }

    if (slideData.order && (slideData.order < 0 || slideData.order > 100)) {
      errors.push('Order must be between 0 and 100');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // ========== PRIVATE METHODS ==========

  private loadSlides(): void {
    // Load active slides for public use
    this.getActiveSlides().subscribe({
      next: (slides) => {
        console.log('Active slides loaded:', slides.length);
      },
      error: (error) => {
        console.error('Failed to load active slides:', error);
        // Set empty array on error to prevent UI issues
        this.activeSlidesSubject.next([]);
      }
    });
  }

  private getSampleSlides(): CarouselSlide[] {
    return [
      {
        id: 'sample-1',
        title: 'Welcome to BuyFusion',
        subtitle: 'Your One-Stop E-commerce Solution',
        description: 'Discover amazing products at great prices',
        image: {
          url: 'https://via.placeholder.com/1200x400/3b82f6/ffffff?text=Welcome+to+BuyFusion',
          public_id: 'sample-1'
        },
        buttonText: 'Shop Now',
        buttonLink: '/products',
        isActive: true,
        order: 1,
        startDate: new Date().toISOString(),
        endDate: null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: 'sample-2',
        title: 'Special Offers',
        subtitle: 'Up to 50% Off',
        description: 'Limited time deals on electronics and fashion',
        image: {
          url: 'https://via.placeholder.com/1200x400/ef4444/ffffff?text=Special+Offers',
          public_id: 'sample-2'
        },
        buttonText: 'View Offers',
        buttonLink: '/offers',
        isActive: true,
        order: 2,
        startDate: new Date().toISOString(),
        endDate: null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];
  }

  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      // Check if it's a relative URL
      return url.startsWith('/') || url.startsWith('#');
    }
  }

  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred';

    // Check if we're in browser environment and ErrorEvent exists
    if (typeof window !== 'undefined' && typeof ErrorEvent !== 'undefined' && error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Server-side error or network error
      errorMessage = error.error?.message || error.error?.error || `Error Code: ${error.status}\nMessage: ${error.message}`;
    }

    console.error('CarouselService Error:', errorMessage);
    return throwError(() => new Error(errorMessage));
  }
}

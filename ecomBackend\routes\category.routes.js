const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../middlewares/verifyToken');
const authorize = require('../middlewares/authorize');
const rateLimit = require('express-rate-limit');
const { uploadCategoryImage } = require('../middlewares/fileUpload');
const categoryController = require('../controllers/category.controller');

// Rate limiting for category operations
const categoryLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 50, // 50 requests per 15 minutes
  message: 'Too many category requests, please try again later.',
});

/**
 * @route GET /api/categories
 * @desc List all product categories
 * @access Public
 */
router.get(
  '/',
  categoryLimiter,
  categoryController.getAllCategories
);

/**
 * @route GET /api/categories/tree/hierarchy
 * @desc Get category tree with hierarchy
 * @access Public
 */
router.get(
  '/tree/hierarchy',
  categoryLimiter,
  categoryController.getCategoryTree
);

/**
 * @route GET /api/categories/:id
 * @desc Get category by ID with subcategories
 * @access Public
 */
router.get(
  '/:id',
  categoryLimiter,
  categoryController.getCategoryById
);

/**
 * @route GET /api/categories/:id/products
 * @desc Get products in a specific category
 * @access Public
 */
router.get(
  '/:id/products',
  categoryLimiter,
  categoryController.getProductsByCategory
);

/**
 * @route POST /api/categories
 * @desc Add new category (Admin only)
 * @access Private (admin)
 */
router.post(
  '/',
  verifyToken,
  authorizeRoles('admin'),
  authorize(['create:category']),
  uploadCategoryImage,
  categoryController.createCategory
);

/**
 * @route PUT /api/categories/:id
 * @desc Update category (Admin only)
 * @access Private (admin)
 */
router.put(
  '/:id',
  verifyToken,
  authorizeRoles('admin'),
  authorize(['edit:category']),
  uploadCategoryImage,
  categoryController.updateCategory
);

/**
 * @route DELETE /api/categories/:id
 * @desc Remove category (Admin only)
 * @access Private (admin)
 */
router.delete(
  '/:id',
  verifyToken,
  authorizeRoles('admin'),
  authorize(['delete:category']),
  categoryController.deleteCategory
);

/**
 * @route PUT /api/categories/:id/reorder
 * @desc Reorder categories (Admin only)
 * @access Private (admin)
 */
router.put(
  '/:id/reorder',
  verifyToken,
  authorizeRoles('admin'),
  authorize(['edit:category']),
  categoryController.reorderCategories
);

/**
 * @route PUT /api/categories/:id/toggle-status
 * @desc Toggle category active status (Admin only)
 * @access Private (admin)
 */
router.put(
  '/:id/toggle-status',
  verifyToken,
  authorizeRoles('admin'),
  authorize(['edit:category']),
  categoryController.toggleCategoryStatus
);

module.exports = router;

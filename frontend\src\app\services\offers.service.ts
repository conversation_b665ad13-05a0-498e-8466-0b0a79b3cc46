import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError, BehaviorSubject, of } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { environment } from '../../environments/environment';

// Interfaces
export interface Offer {
  id: string;
  _id: string;
  title: string;
  description: string;
  image?: {
    url: string;
    public_id: string;
  };
  discountType: 'percentage' | 'fixed';
  discountValue: number;
  minOrderAmount?: number;
  maxDiscountAmount?: number;
  applicableProducts?: string[];
  applicableCategories?: string[];
  isActive: boolean;
  startDate: string;
  endDate: string;
  usageLimit?: number;
  usedCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface Coupon {
  id: string;
  _id: string;
  code: string;
  title: string;
  description: string;
  discountType: 'percentage' | 'fixed';
  discountValue: number;
  minOrderAmount?: number;
  maxDiscountAmount?: number;
  applicableProducts?: string[];
  applicableCategories?: string[];
  isActive: boolean;
  startDate: string;
  endDate: string;
  usageLimit?: number;
  usedCount: number;
  userUsageLimit?: number;
  isPublic: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CouponValidation {
  isValid: boolean;
  message: string;
  discount?: number;
  coupon?: Coupon;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

@Injectable({
  providedIn: 'root'
})
export class OffersService {
  private readonly apiUrl = `${environment.apiBaseUrl}/api`;

  // Cache for better performance
  private offersSubject = new BehaviorSubject<Offer[]>([]);
  public offers$ = this.offersSubject.asObservable();

  private activeOffersSubject = new BehaviorSubject<Offer[]>([]);
  public activeOffers$ = this.activeOffersSubject.asObservable();

  private couponsSubject = new BehaviorSubject<Coupon[]>([]);
  public coupons$ = this.couponsSubject.asObservable();

  constructor(private http: HttpClient) {
    // Load offers and coupons on service initialization
    this.loadActiveOffers();
  }

  // ========== OFFERS METHODS ==========

  /**
   * Get all offers (Admin)
   */
  getAllOffers(page: number = 1, limit: number = 10): Observable<PaginatedResponse<Offer>> {
    return this.http.get<PaginatedResponse<Offer>>(`${this.apiUrl}/offers/admin?page=${page}&limit=${limit}`)
      .pipe(
        tap(response => this.offersSubject.next(response.items || [])),
        catchError(this.handleError)
      );
  }

  /**
   * Get active offers (Public)
   */
  getActiveOffers(): Observable<Offer[]> {
    return this.http.get<{ offers: Offer[] }>(`${this.apiUrl}/offers/active`)
      .pipe(
        map(response => response.offers || []),
        tap(offers => this.activeOffersSubject.next(offers)),
        catchError(error => {
          console.warn('Offers API not available, using empty array');
          this.activeOffersSubject.next([]);
          return of([]);
        })
      );
  }

  /**
   * Get offer by ID
   */
  getOfferById(offerId: string): Observable<Offer> {
    return this.http.get<{ offer: Offer }>(`${this.apiUrl}/offers/${offerId}`)
      .pipe(
        map(response => response.offer),
        catchError(this.handleError)
      );
  }

  /**
   * Create new offer (Admin)
   */
  createOffer(offerData: FormData): Observable<Offer> {
    return this.http.post<{ offer: Offer }>(`${this.apiUrl}/offers`, offerData)
      .pipe(
        map(response => response.offer),
        tap(() => this.refreshOffers()),
        catchError(this.handleError)
      );
  }

  /**
   * Update offer (Admin)
   */
  updateOffer(offerId: string, offerData: FormData): Observable<Offer> {
    return this.http.put<{ offer: Offer }>(`${this.apiUrl}/offers/${offerId}`, offerData)
      .pipe(
        map(response => response.offer),
        tap(() => this.refreshOffers()),
        catchError(this.handleError)
      );
  }

  /**
   * Delete offer (Admin)
   */
  deleteOffer(offerId: string): Observable<{ message: string }> {
    return this.http.delete<{ message: string }>(`${this.apiUrl}/offers/${offerId}`)
      .pipe(
        tap(() => this.refreshOffers()),
        catchError(this.handleError)
      );
  }

  /**
   * Toggle offer status (Admin)
   */
  toggleOfferStatus(offerId: string, isActive: boolean): Observable<Offer> {
    return this.http.patch<{ offer: Offer }>(`${this.apiUrl}/offers/${offerId}/status`, { isActive })
      .pipe(
        map(response => response.offer),
        tap(() => this.refreshOffers()),
        catchError(this.handleError)
      );
  }

  // ========== COUPONS METHODS ==========

  /**
   * Get all coupons (Admin)
   */
  getAllCoupons(page: number = 1, limit: number = 10): Observable<PaginatedResponse<Coupon>> {
    return this.http.get<PaginatedResponse<Coupon>>(`${this.apiUrl}/coupons/admin?page=${page}&limit=${limit}`)
      .pipe(
        tap(response => this.couponsSubject.next(response.items || [])),
        catchError(this.handleError)
      );
  }

  /**
   * Get public coupons
   */
  getPublicCoupons(): Observable<Coupon[]> {
    return this.http.get<{ coupons: Coupon[] }>(`${this.apiUrl}/coupons/public`)
      .pipe(
        map(response => response.coupons || []),
        catchError(this.handleError)
      );
  }

  /**
   * Get coupon by ID
   */
  getCouponById(couponId: string): Observable<Coupon> {
    return this.http.get<{ coupon: Coupon }>(`${this.apiUrl}/coupons/${couponId}`)
      .pipe(
        map(response => response.coupon),
        catchError(this.handleError)
      );
  }

  /**
   * Validate coupon code
   */
  validateCoupon(code: string, orderAmount: number, productIds?: string[]): Observable<CouponValidation> {
    const payload = { code, orderAmount, productIds };
    return this.http.post<CouponValidation>(`${this.apiUrl}/coupons/validate`, payload)
      .pipe(catchError(this.handleError));
  }

  /**
   * Apply coupon to order
   */
  applyCoupon(code: string, orderId: string): Observable<{ message: string; discount: number }> {
    return this.http.post<{ message: string; discount: number }>(`${this.apiUrl}/coupons/apply`, { code, orderId })
      .pipe(catchError(this.handleError));
  }

  /**
   * Create new coupon (Admin)
   */
  createCoupon(couponData: any): Observable<Coupon> {
    return this.http.post<{ coupon: Coupon }>(`${this.apiUrl}/coupons`, couponData)
      .pipe(
        map(response => response.coupon),
        tap(() => this.refreshCoupons()),
        catchError(this.handleError)
      );
  }

  /**
   * Update coupon (Admin)
   */
  updateCoupon(couponId: string, couponData: any): Observable<Coupon> {
    return this.http.put<{ coupon: Coupon }>(`${this.apiUrl}/coupons/${couponId}`, couponData)
      .pipe(
        map(response => response.coupon),
        tap(() => this.refreshCoupons()),
        catchError(this.handleError)
      );
  }

  /**
   * Delete coupon (Admin)
   */
  deleteCoupon(couponId: string): Observable<{ message: string }> {
    return this.http.delete<{ message: string }>(`${this.apiUrl}/coupons/${couponId}`)
      .pipe(
        tap(() => this.refreshCoupons()),
        catchError(this.handleError)
      );
  }

  /**
   * Toggle coupon status (Admin)
   */
  toggleCouponStatus(couponId: string, isActive: boolean): Observable<Coupon> {
    return this.http.patch<{ coupon: Coupon }>(`${this.apiUrl}/coupons/${couponId}/status`, { isActive })
      .pipe(
        map(response => response.coupon),
        tap(() => this.refreshCoupons()),
        catchError(this.handleError)
      );
  }

  // ========== UTILITY METHODS ==========

  /**
   * Calculate discount amount
   */
  calculateDiscount(
    discountType: 'percentage' | 'fixed',
    discountValue: number,
    orderAmount: number,
    maxDiscountAmount?: number
  ): number {
    let discount = 0;

    if (discountType === 'percentage') {
      discount = (orderAmount * discountValue) / 100;
      if (maxDiscountAmount && discount > maxDiscountAmount) {
        discount = maxDiscountAmount;
      }
    } else {
      discount = Math.min(discountValue, orderAmount);
    }

    return Math.round(discount * 100) / 100; // Round to 2 decimal places
  }

  /**
   * Check if offer/coupon is currently valid
   */
  isCurrentlyValid(item: Offer | Coupon): boolean {
    if (!item.isActive) return false;

    const now = new Date();
    const startDate = new Date(item.startDate);
    const endDate = new Date(item.endDate);

    return now >= startDate && now <= endDate;
  }

  /**
   * Generate random coupon code
   */
  generateCouponCode(prefix: string = 'SAVE', length: number = 8): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = prefix;
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * Refresh offers cache
   */
  refreshOffers(): void {
    this.loadActiveOffers();
  }

  /**
   * Refresh coupons cache
   */
  refreshCoupons(): void {
    this.getPublicCoupons().subscribe({
      next: (coupons) => {
        console.log('Public coupons loaded:', coupons.length);
      },
      error: (error) => {
        console.error('Failed to load public coupons:', error);
      }
    });
  }

  /**
   * Get cached offers (synchronous)
   */
  getCachedOffers(): Offer[] {
    return this.offersSubject.value;
  }

  /**
   * Get cached active offers (synchronous)
   */
  getCachedActiveOffers(): Offer[] {
    return this.activeOffersSubject.value;
  }

  /**
   * Get cached coupons (synchronous)
   */
  getCachedCoupons(): Coupon[] {
    return this.couponsSubject.value;
  }

  // ========== PRIVATE METHODS ==========

  private loadActiveOffers(): void {
    // Provide empty data immediately to prevent loading issues
    this.activeOffersSubject.next([]);
    console.log('Sample offers loaded: 0 (no offers available)');

    // Try to load real data in background (optional)
    this.getActiveOffers().subscribe({
      next: (offers) => {
        if (offers.length > 0) {
          this.activeOffersSubject.next(offers);
          console.log('Real offers loaded:', offers.length);
        }
      },
      error: (error) => {
        console.warn('Offers API not available, using empty array');
      }
    });
  }

  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred';

    // Check if we're in browser environment and ErrorEvent exists
    if (typeof window !== 'undefined' && typeof ErrorEvent !== 'undefined' && error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Server-side error or network error
      errorMessage = error.error?.message || error.error?.error || `Error Code: ${error.status}\nMessage: ${error.message}`;
    }

    console.error('OffersService Error:', errorMessage);
    return throwError(() => new Error(errorMessage));
  }
}


<!-- Loading Indicator -->
<div *ngIf="isLoading" class="fixed inset-0 bg-white z-50 flex items-center justify-center">
  <div class="text-center">
    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
    <p class="mt-4 text-gray-600">Loading BuyFusion...</p>
  </div>
</div>

<!-- Main App Content -->
<div class="min-h-screen flex flex-col" *ngIf="!isLoading && !hasError">
  <!-- Navbar with error boundary -->
  <div class="navbar-container">
    <app-navbar></app-navbar>
  </div>

  <!-- Main content -->
  <main class="flex-grow">
    <router-outlet></router-outlet>
  </main>

  <!-- Footer with error boundary -->
  <div class="footer-container">
    <app-footer></app-footer>
  </div>
</div>

<!-- Error Fallback -->
<div *ngIf="hasError" class="min-h-screen flex items-center justify-center bg-gray-50">
  <div class="text-center">
    <h1 class="text-2xl font-bold text-gray-900 mb-4">Something went wrong</h1>
    <p class="text-gray-600 mb-4">We're having trouble loading the application.</p>
    <button (click)="reload()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
      Reload Page
    </button>
  </div>
</div>
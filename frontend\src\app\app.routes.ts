import { Routes } from '@angular/router';

// Guards
import { AuthGuard } from './guards/auth.guard';
import { RoleGuard } from './guards/role.guard';

// Public Pages
import { HomeComponent } from './page/home/<USER>';
import { LoginComponent } from './page/login/login.component';
import { SignupComponent } from './page/signup/signup.component';
import { ForgotPasswordComponent } from './page/forgot-password/forgot-password.component';

import { AboutComponent } from './page/about/about.component';
import { ContactComponent } from './page/contact/contact.component';
import { ProductDetailsComponent } from './page/product-details/product-details.component';
import { ProductCategoryComponent } from './page/product-category/product-category.component';
import { WishlistComponent } from './page/wishlist/wishlist.component';

// User Pages
import { UserProfileComponent } from './page/user-profile/user-profile.component';
import { CartComponent } from './page/cart/cart.component';
import { CheckoutComponent } from './page/checkout/checkout.component';
import { OrderSuccessComponent } from './page/order-success/order-success.component';

// Admin Pages
import { AdminDashboardComponent } from './admin-dashboard/admin-dashboard.component';
import { SellerManagementComponent } from './admin-dashboard/seller-management/seller-management.component';
import { UserManagementComponent } from './admin-dashboard/user-management/user-management.component';
import { OrderManagementComponent } from './admin-dashboard/order-management/order-management.component';
import { ProductManagementComponent } from './admin-dashboard/product-management/product-management.component';
import { CommissionManagementComponent } from './admin-dashboard/commission-management/commission-management.component';
import { AdvancedCommissionComponent } from './admin-dashboard/advanced-commission/advanced-commission.component';
import { LoginAdminComponent } from './pages/login-admin/login-admin.component';
import { RegisterAdminComponent } from './pages/register-admin/register-admin.component';

// Delivery Partner Pages
import { DeliveryDashboardComponent } from './delivery-dashboard/delivery-dashboard.component';

// Customer Pages
import { CustomerDashboardComponent } from './customer-dashboard/customer-dashboard.component';

// Seller Pages
import { SellerDashboardComponent } from './seller-dashboard/seller-dashboard.component';
import { SellerOrdersComponent } from './seller-dashboard/seller-orders/seller-orders.component';
import { SellerProductsComponent } from './seller-dashboard/seller-products/seller-products.component';
import { ProductaddComponent } from './seller-dashboard/productadd/productadd.component';
import { EditProductComponent } from './seller-dashboard/editproduct/editproduct.component';
import { RegisterSellerComponent } from './pages/register-seller/register-seller.component';



// Misc
import { UnauthorizedComponent } from './unauthorized/unauthorized.component';
import { NotFoundComponent } from './page/not-found/not-found.component';
import { PendingApprovalComponent } from './page/pending-approval/pending-approval.component';
import { AccountRejectedComponent } from './page/account-rejected/account-rejected.component';
import { NotificationsComponent } from './pages/notifications/notifications.component';


export const routes: Routes = [
  // 🌐 Public Routes
  { path: '', component: HomeComponent },
  { path: 'login', component: LoginComponent },
  { path: 'signup', component: SignupComponent },
  { path: 'forgot-password', component: ForgotPasswordComponent },
  { path: 'forgot-password/:token', component: ForgotPasswordComponent },
  { path: 'reset-password', component: ForgotPasswordComponent },
  { path: 'reset-password/:token', component: ForgotPasswordComponent },

  { path: 'about', component: AboutComponent },
  { path: 'contact', component: ContactComponent },
  { path: 'product/:id', component: ProductDetailsComponent },
  { path: 'products/:id', component: ProductDetailsComponent },
  { path: 'product-category/:category', component: ProductCategoryComponent },
  { path: 'products/category/:category', component: ProductCategoryComponent },
  { path: 'products', component: ProductCategoryComponent },
  { path: 'register-seller', component: RegisterSellerComponent },
  { path: 'admin-setup', component: RegisterAdminComponent },
  { path: 'admin-login', component: LoginAdminComponent },
  { path: 'pending-approval', component: PendingApprovalComponent },
  { path: 'account-rejected', component: AccountRejectedComponent },


  // 🛒 User Routes
  {
    path: 'cart',
    component: CartComponent,
    canActivate: [AuthGuard],
    data: { roles: ['customer', 'seller', 'admin'] }
  },
  {
    path: 'checkout',
    component: CheckoutComponent,
    canActivate: [AuthGuard],
    data: { roles: ['customer', 'seller', 'admin'] }
  },
  {
    path: 'order-success',
    component: OrderSuccessComponent,
    canActivate: [AuthGuard],
    data: { roles: ['customer', 'seller', 'admin'] }
  },
  {
    path: 'wishlist',
    component: WishlistComponent,
    canActivate: [AuthGuard],
    data: { roles: ['customer', 'seller', 'admin'] }
  },
  {
    path: 'user-profile',
    component: UserProfileComponent,
    canActivate: [AuthGuard],
    data: { roles: ['customer', 'seller', 'admin'] }
  },
  {
    path: 'user-profile/:id',
    component: UserProfileComponent,
    canActivate: [AuthGuard],
    data: { roles: ['customer', 'seller', 'admin'] }
  },
  {
    path: 'notifications',
    component: NotificationsComponent,
    canActivate: [AuthGuard],
    data: { roles: ['customer', 'seller', 'admin', 'delivery_partner'] }
  },

  // 🚚 Delivery Partner Routes
  {
    path: 'delivery-dashboard',
    component: DeliveryDashboardComponent,
    canActivate: [AuthGuard, RoleGuard],
    data: {
      expectedRoles: ['delivery_partner'],
      permissions: ['view:delivery-dashboard'],
      allowedStatuses: ['active', 'verified'],
      requireAllPermissions: false
    }
  },

  // 👤 Customer Routes
  {
    path: 'customer-dashboard',
    component: CustomerDashboardComponent,
    canActivate: [AuthGuard, RoleGuard],
    data: {
      expectedRoles: ['customer'],
      permissions: ['view:customer-dashboard'],
      allowedStatuses: ['active', 'verified'],
      requireAllPermissions: false
    }
  },

  // 🛡️ Admin Routes
  {
    path: 'admin-dashboard',
    component: AdminDashboardComponent,
    canActivate: [AuthGuard, RoleGuard],
    data: { roles: ['admin'], expectedRoles: ['admin'] },
    children: [
      { path: '', redirectTo: 'user-management', pathMatch: 'full' },
      { path: 'seller-management', component: SellerManagementComponent },
      { path: 'user-management', component: UserManagementComponent },
      { path: 'order-management', component: OrderManagementComponent },
      { path: 'product-management', component: ProductManagementComponent },
      { path: 'commission-management', component: CommissionManagementComponent },
      { path: 'advanced-commission', component: AdvancedCommissionComponent },
      // Additional routes from admin module
      { path: 'users-management', component: UserManagementComponent },
      { path: 'admin-orders', component: OrderManagementComponent },
      { path: 'products-management', component: ProductManagementComponent }
    ]
  },

  // 🏬 Seller Routes
  {
    path: 'seller-dashboard',
    component: SellerDashboardComponent,
    canActivate: [AuthGuard, RoleGuard],
    data: {
      expectedRoles: ['seller'],
      permissions: ['view:seller-dashboard'],
      allowedStatuses: ['active', 'verified'],
      requireAllPermissions: false
    },
    children: [
      { path: '', redirectTo: 'products', pathMatch: 'full' },
      {
        path: 'sellerproduct-add',
        component: ProductaddComponent,
        canActivate: [AuthGuard, RoleGuard],
        data: {
          expectedRoles: ['seller'],
          permissions: ['add:product'],
          allowedStatuses: ['active', 'verified']
        }
      },
      {
        path: 'seller-orders',
        component: SellerOrdersComponent,
        canActivate: [AuthGuard, RoleGuard],
        data: {
          expectedRoles: ['seller'],
          permissions: ['view:order', 'manage:order'],
          allowedStatuses: ['active', 'verified'],
          requireAllPermissions: false
        }
      },
      {
        path: 'products',
        component: SellerProductsComponent,
        canActivate: [AuthGuard, RoleGuard],
        data: {
          expectedRoles: ['seller'],
          permissions: ['view:product', 'manage:product'],
          allowedStatuses: ['active', 'verified'],
          requireAllPermissions: false
        }
      },
      {
        path: 'products/edit/:id',
        component: EditProductComponent,
        canActivate: [AuthGuard, RoleGuard],
        data: {
          expectedRoles: ['seller'],
          permissions: ['edit:product'],
          allowedStatuses: ['active', 'verified']
        }
      },

    ]
  },



  // 🚫 Unauthorized / Not Found
  { path: 'unauthorized', component: UnauthorizedComponent },
  { path: '**', component: NotFoundComponent }
];


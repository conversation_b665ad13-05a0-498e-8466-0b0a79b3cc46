const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../middlewares/verifyToken');
const {
  getActiveSlides,
  getAllSlides,
  getSlideById,
  createSlide,
  updateSlide,
  deleteSlide,
  toggleSlideStatus,
  reorderSlides
} = require('../controllers/carousel.controller');

/**
 * @route GET /api/carousel/active
 * @desc Get active carousel slides for public display
 * @access Public
 */
router.get('/active', getActiveSlides);

/**
 * @route GET /api/carousel
 * @desc Get all carousel slides with pagination
 * @access Private (Admin only)
 */
router.get('/', verifyToken, authorizeRoles('admin'), getAllSlides);

/**
 * @route GET /api/carousel/:id
 * @desc Get carousel slide by ID
 * @access Private (Admin only)
 */
router.get('/:id', verifyToken, authorizeRoles('admin'), getSlideById);

/**
 * @route POST /api/carousel
 * @desc Create new carousel slide
 * @access Private (Admin only)
 */
router.post('/', verifyToken, authorizeRoles('admin'), createSlide);

/**
 * @route PUT /api/carousel/:id
 * @desc Update carousel slide
 * @access Private (Admin only)
 */
router.put('/:id', verifyToken, authorizeRoles('admin'), updateSlide);

/**
 * @route DELETE /api/carousel/:id
 * @desc Delete carousel slide
 * @access Private (Admin only)
 */
router.delete('/:id', verifyToken, authorizeRoles('admin'), deleteSlide);

/**
 * @route PATCH /api/carousel/:id/toggle
 * @desc Toggle carousel slide status
 * @access Private (Admin only)
 */
router.patch('/:id/toggle', verifyToken, authorizeRoles('admin'), toggleSlideStatus);

/**
 * @route PUT /api/carousel/reorder
 * @desc Reorder carousel slides
 * @access Private (Admin only)
 */
router.put('/reorder', verifyToken, authorizeRoles('admin'), reorderSlides);

module.exports = router;

import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError, BehaviorSubject } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { environment } from '../../environments/environment';

// Interfaces
export interface Category {
  id: string;
  _id: string;
  name: string;
  description?: string;
  image?: {
    url: string;
    public_id: string;
  };
  parentCategory?: string;
  subcategories?: Category[];
  isActive: boolean;
  productCount?: number;
  commissionRate?: number;
  createdAt: string;
  updatedAt: string;
}

export interface CategoryTree {
  id: string;
  name: string;
  image?: {
    url: string;
    public_id: string;
  };
  subcategories: CategoryTree[];
  productCount: number;
}

@Injectable({
  providedIn: 'root'
})
export class CategoryService {
  private readonly apiUrl = `${environment.apiBaseUrl}/api/categories`;

  // Cache categories for better performance
  private categoriesSubject = new BehaviorSubject<Category[]>([]);
  public categories$ = this.categoriesSubject.asObservable();

  private categoryTreeSubject = new BehaviorSubject<CategoryTree[]>([]);
  public categoryTree$ = this.categoryTreeSubject.asObservable();

  constructor(private http: HttpClient) {
    // Load categories on service initialization
    this.loadCategories();
  }

  // ========== PUBLIC METHODS ==========

  /**
   * Get all categories
   */
  getCategories(): Observable<Category[]> {
    return this.http.get<{ categories: Category[] }>(`${this.apiUrl}`)
      .pipe(
        map(response => response.categories || []),
        tap(categories => this.categoriesSubject.next(categories)),
        catchError(this.handleError)
      );
  }

  /**
   * Get category tree (hierarchical structure)
   */
  getCategoryTree(): Observable<CategoryTree[]> {
    return this.http.get<{ categoryTree: CategoryTree[] }>(`${this.apiUrl}/tree/hierarchy`)
      .pipe(
        map(response => response.categoryTree || []),
        tap(tree => this.categoryTreeSubject.next(tree)),
        catchError(this.handleError)
      );
  }

  /**
   * Get category by ID
   */
  getCategoryById(categoryId: string): Observable<Category> {
    return this.http.get<{ category: Category }>(`${this.apiUrl}/${categoryId}`)
      .pipe(
        map(response => response.category),
        catchError(this.handleError)
      );
  }

  /**
   * Get popular categories (most products)
   */
  getPopularCategories(limit: number = 8): Observable<Category[]> {
    return this.http.get<{ categories: Category[] }>(`${this.apiUrl}/popular?limit=${limit}`)
      .pipe(
        map(response => response.categories || []),
        catchError(this.handleError)
      );
  }

  /**
   * Search categories by name
   */
  searchCategories(searchTerm: string): Observable<Category[]> {
    return this.http.get<{ categories: Category[] }>(`${this.apiUrl}/search?q=${encodeURIComponent(searchTerm)}`)
      .pipe(
        map(response => response.categories || []),
        catchError(this.handleError)
      );
  }

  /**
   * Get main categories (parent categories only)
   */
  getMainCategories(): Observable<Category[]> {
    return this.categories$.pipe(
      map(categories => categories.filter(cat => !cat.parentCategory && cat.isActive))
    );
  }

  /**
   * Get subcategories for a parent category
   */
  getSubcategories(parentCategoryId: string): Observable<Category[]> {
    return this.categories$.pipe(
      map(categories => categories.filter(cat => cat.parentCategory === parentCategoryId && cat.isActive))
    );
  }

  /**
   * Refresh categories cache
   */
  refreshCategories(): void {
    this.loadCategories();
  }

  /**
   * Get cached categories (synchronous)
   */
  getCachedCategories(): Category[] {
    return this.categoriesSubject.value;
  }

  /**
   * Get cached category tree (synchronous)
   */
  getCachedCategoryTree(): CategoryTree[] {
    return this.categoryTreeSubject.value;
  }

  // ========== ADMIN METHODS (Protected) ==========

  /**
   * Create new category (Admin only)
   */
  createCategory(categoryData: FormData): Observable<Category> {
    return this.http.post<{ category: Category }>(`${this.apiUrl}`, categoryData)
      .pipe(
        map(response => response.category),
        tap(() => this.refreshCategories()),
        catchError(this.handleError)
      );
  }

  /**
   * Update category (Admin only)
   */
  updateCategory(categoryId: string, categoryData: FormData): Observable<Category> {
    return this.http.put<{ category: Category }>(`${this.apiUrl}/${categoryId}`, categoryData)
      .pipe(
        map(response => response.category),
        tap(() => this.refreshCategories()),
        catchError(this.handleError)
      );
  }

  /**
   * Delete category (Admin only)
   */
  deleteCategory(categoryId: string): Observable<{ message: string }> {
    return this.http.delete<{ message: string }>(`${this.apiUrl}/${categoryId}`)
      .pipe(
        tap(() => this.refreshCategories()),
        catchError(this.handleError)
      );
  }

  /**
   * Toggle category status (Admin only)
   */
  toggleCategoryStatus(categoryId: string, isActive: boolean): Observable<Category> {
    return this.http.patch<{ category: Category }>(`${this.apiUrl}/${categoryId}/status`, { isActive })
      .pipe(
        map(response => response.category),
        tap(() => this.refreshCategories()),
        catchError(this.handleError)
      );
  }

  // ========== UTILITY METHODS ==========

  /**
   * Get category path (breadcrumb)
   */
  getCategoryPath(categoryId: string): string[] {
    const categories = this.getCachedCategories();
    const path: string[] = [];

    let currentCategory = categories.find(cat => cat.id === categoryId || cat._id === categoryId);

    while (currentCategory) {
      path.unshift(currentCategory.name);
      if (currentCategory.parentCategory) {
        currentCategory = categories.find(cat =>
          cat.id === currentCategory!.parentCategory || cat._id === currentCategory!.parentCategory
        );
      } else {
        break;
      }
    }

    return path;
  }

  /**
   * Check if category has subcategories
   */
  hasSubcategories(categoryId: string): boolean {
    const categories = this.getCachedCategories();
    return categories.some(cat => cat.parentCategory === categoryId);
  }

  /**
   * Get category display name with product count
   */
  getCategoryDisplayName(category: Category): string {
    const count = category.productCount || 0;
    return `${category.name} (${count})`;
  }

  // ========== PRIVATE METHODS ==========

  private loadCategories(): void {
    this.getCategories().subscribe({
      next: (categories) => {
        console.log('Categories loaded:', categories.length);
      },
      error: (error) => {
        console.error('Failed to load categories:', error);
        // Set empty array on error to prevent UI issues
        this.categoriesSubject.next([]);
      }
    });

    this.getCategoryTree().subscribe({
      next: (tree) => {
        console.log('Category tree loaded:', tree.length);
      },
      error: (error) => {
        console.error('Failed to load category tree:', error);
        // Set empty array on error to prevent UI issues
        this.categoryTreeSubject.next([]);
      }
    });
  }

  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred';

    // Check if we're in browser environment and ErrorEvent exists
    if (typeof window !== 'undefined' && typeof ErrorEvent !== 'undefined' && error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Server-side error or network error
      errorMessage = error.error?.message || error.error?.error || `Error Code: ${error.status}\nMessage: ${error.message}`;
    }

    console.error('CategoryService Error:', errorMessage);
    return throwError(() => new Error(errorMessage));
  }
}

const Carousel = require('../models/carousel.model');
const logger = require('../utils/logger');
const { AppError } = require('../utils/errors');

/**
 * @desc Get active carousel slides for public display
 * @route GET /api/carousel/active
 * @access Public
 */
exports.getActiveSlides = async (req, res) => {
  try {
    const slides = await Carousel.getActiveSlides();
    
    logger.info(`Retrieved ${slides.length} active carousel slides`);
    
    res.status(200).json({
      success: true,
      count: slides.length,
      slides: slides
    });
  } catch (error) {
    logger.error(`Error retrieving active carousel slides: ${error.message}`);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve carousel slides',
      error: error.message
    });
  }
};

/**
 * @desc Get all carousel slides (Admin only)
 * @route GET /api/carousel
 * @access Private (Admin)
 */
exports.getAllSlides = async (req, res) => {
  try {
    const { page = 1, limit = 10, isActive } = req.query;
    
    const query = {};
    if (isActive !== undefined) {
      query.isActive = isActive === 'true';
    }
    
    const slides = await Carousel.find(query)
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email')
      .sort({ order: 1, createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);
    
    const total = await Carousel.countDocuments(query);
    
    logger.info(`Retrieved ${slides.length} carousel slides for admin`);
    
    res.status(200).json({
      success: true,
      count: slides.length,
      total,
      page: parseInt(page),
      pages: Math.ceil(total / limit),
      slides: slides
    });
  } catch (error) {
    logger.error(`Error retrieving carousel slides: ${error.message}`);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve carousel slides',
      error: error.message
    });
  }
};

/**
 * @desc Get carousel slide by ID
 * @route GET /api/carousel/:id
 * @access Private (Admin)
 */
exports.getSlideById = async (req, res) => {
  try {
    const slide = await Carousel.findById(req.params.id)
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email');
    
    if (!slide) {
      return res.status(404).json({
        success: false,
        message: 'Carousel slide not found'
      });
    }
    
    logger.info(`Retrieved carousel slide: ${slide._id}`);
    
    res.status(200).json({
      success: true,
      slide: slide
    });
  } catch (error) {
    logger.error(`Error retrieving carousel slide: ${error.message}`);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve carousel slide',
      error: error.message
    });
  }
};

/**
 * @desc Create new carousel slide
 * @route POST /api/carousel
 * @access Private (Admin)
 */
exports.createSlide = async (req, res) => {
  try {
    const slideData = {
      ...req.body,
      createdBy: req.user.id
    };
    
    const slide = new Carousel(slideData);
    await slide.save();
    
    logger.info(`Created new carousel slide: ${slide._id}`);
    
    res.status(201).json({
      success: true,
      message: 'Carousel slide created successfully',
      slide: slide
    });
  } catch (error) {
    logger.error(`Error creating carousel slide: ${error.message}`);
    res.status(400).json({
      success: false,
      message: 'Failed to create carousel slide',
      error: error.message
    });
  }
};

/**
 * @desc Update carousel slide
 * @route PUT /api/carousel/:id
 * @access Private (Admin)
 */
exports.updateSlide = async (req, res) => {
  try {
    const updateData = {
      ...req.body,
      updatedBy: req.user.id
    };
    
    const slide = await Carousel.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    );
    
    if (!slide) {
      return res.status(404).json({
        success: false,
        message: 'Carousel slide not found'
      });
    }
    
    logger.info(`Updated carousel slide: ${slide._id}`);
    
    res.status(200).json({
      success: true,
      message: 'Carousel slide updated successfully',
      slide: slide
    });
  } catch (error) {
    logger.error(`Error updating carousel slide: ${error.message}`);
    res.status(400).json({
      success: false,
      message: 'Failed to update carousel slide',
      error: error.message
    });
  }
};

/**
 * @desc Delete carousel slide
 * @route DELETE /api/carousel/:id
 * @access Private (Admin)
 */
exports.deleteSlide = async (req, res) => {
  try {
    const slide = await Carousel.findByIdAndDelete(req.params.id);
    
    if (!slide) {
      return res.status(404).json({
        success: false,
        message: 'Carousel slide not found'
      });
    }
    
    logger.info(`Deleted carousel slide: ${slide._id}`);
    
    res.status(200).json({
      success: true,
      message: 'Carousel slide deleted successfully'
    });
  } catch (error) {
    logger.error(`Error deleting carousel slide: ${error.message}`);
    res.status(500).json({
      success: false,
      message: 'Failed to delete carousel slide',
      error: error.message
    });
  }
};

/**
 * @desc Toggle carousel slide status
 * @route PATCH /api/carousel/:id/toggle
 * @access Private (Admin)
 */
exports.toggleSlideStatus = async (req, res) => {
  try {
    const slide = await Carousel.findById(req.params.id);
    
    if (!slide) {
      return res.status(404).json({
        success: false,
        message: 'Carousel slide not found'
      });
    }
    
    slide.isActive = !slide.isActive;
    slide.updatedBy = req.user.id;
    await slide.save();
    
    logger.info(`Toggled carousel slide status: ${slide._id} - ${slide.isActive ? 'Active' : 'Inactive'}`);
    
    res.status(200).json({
      success: true,
      message: `Carousel slide ${slide.isActive ? 'activated' : 'deactivated'} successfully`,
      slide: slide
    });
  } catch (error) {
    logger.error(`Error toggling carousel slide status: ${error.message}`);
    res.status(500).json({
      success: false,
      message: 'Failed to toggle carousel slide status',
      error: error.message
    });
  }
};

/**
 * @desc Reorder carousel slides
 * @route PUT /api/carousel/reorder
 * @access Private (Admin)
 */
exports.reorderSlides = async (req, res) => {
  try {
    const { slideOrders } = req.body; // Array of { id, order }
    
    if (!Array.isArray(slideOrders)) {
      return res.status(400).json({
        success: false,
        message: 'slideOrders must be an array'
      });
    }
    
    const updatePromises = slideOrders.map(({ id, order }) =>
      Carousel.findByIdAndUpdate(id, { order, updatedBy: req.user.id })
    );
    
    await Promise.all(updatePromises);
    
    logger.info(`Reordered ${slideOrders.length} carousel slides`);
    
    res.status(200).json({
      success: true,
      message: 'Carousel slides reordered successfully'
    });
  } catch (error) {
    logger.error(`Error reordering carousel slides: ${error.message}`);
    res.status(500).json({
      success: false,
      message: 'Failed to reorder carousel slides',
      error: error.message
    });
  }
};

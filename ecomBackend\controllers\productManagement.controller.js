const productManagementService = require('../services/productManagement.service');
const { BadRequestError, NotFoundError } = require('../utils/errors');
const logger = require('../utils/logger');
const multer = require('multer');
const path = require('path');

/**
 * Enhanced Product Management Controller for Commission Management System
 * Handles product CRUD, approval system, bulk upload, and inventory management
 */

// ========== PRODUCT CRUD OPERATIONS ==========

/**
 * Create a new product (Seller)
 */
exports.createProduct = async (req, res) => {
  try {
    const sellerId = req.user.id;
    const productData = req.body;
    const files = req.files || [];

    const product = await productManagementService.createProduct(sellerId, productData, files);

    res.status(201).json({
      success: true,
      message: 'Product created successfully and submitted for approval',
      data: product
    });
  } catch (error) {
    logger.error('Error creating product:', error);
    res.status(error.status || 500).json({
      success: false,
      message: error.message || 'Internal server error',
      error: error.message
    });
  }
};

/**
 * Update product (Seller)
 */
exports.updateProduct = async (req, res) => {
  try {
    const sellerId = req.user.id;
    const { productId } = req.params;
    const updateData = req.body;

    const product = await productManagementService.updateProduct(sellerId, productId, updateData);

    res.status(200).json({
      success: true,
      message: 'Product updated successfully',
      data: product
    });
  } catch (error) {
    logger.error('Error updating product:', error);
    res.status(error.status || 500).json({
      success: false,
      message: error.message || 'Internal server error',
      error: error.message
    });
  }
};

/**
 * Delete product (Seller/Admin)
 */
exports.deleteProduct = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;
    const { productId } = req.params;

    const result = await productManagementService.deleteProduct(userId, productId, userRole);

    res.status(200).json({
      success: true,
      message: result.message
    });
  } catch (error) {
    logger.error('Error deleting product:', error);
    res.status(error.status || 500).json({
      success: false,
      message: error.message || 'Internal server error',
      error: error.message
    });
  }
};

/**
 * Get seller products with filters
 */
exports.getSellerProducts = async (req, res) => {
  try {
    const sellerId = req.user.id;
    const filters = req.query;

    const result = await productManagementService.getSellerProducts(sellerId, filters);

    res.status(200).json({
      success: true,
      message: 'Seller products retrieved successfully',
      data: result
    });
  } catch (error) {
    logger.error('Error getting seller products:', error);
    res.status(error.status || 500).json({
      success: false,
      message: error.message || 'Internal server error',
      error: error.message
    });
  }
};

// ========== PRODUCT APPROVAL SYSTEM ==========

/**
 * Get products pending approval (Admin)
 */
exports.getProductsPendingApproval = async (req, res) => {
  try {
    const { page = 1, limit = 20 } = req.query;

    const result = await productManagementService.getProductsPendingApproval(
      parseInt(page),
      parseInt(limit)
    );

    res.status(200).json({
      success: true,
      message: 'Products pending approval retrieved successfully',
      data: result
    });
  } catch (error) {
    logger.error('Error getting products pending approval:', error);
    res.status(error.status || 500).json({
      success: false,
      message: error.message || 'Internal server error',
      error: error.message
    });
  }
};

/**
 * Approve product (Admin)
 */
exports.approveProduct = async (req, res) => {
  try {
    const adminId = req.user.id;
    const { productId } = req.params;

    const product = await productManagementService.approveProduct(adminId, productId);

    res.status(200).json({
      success: true,
      message: 'Product approved successfully',
      data: product
    });
  } catch (error) {
    logger.error('Error approving product:', error);
    res.status(error.status || 500).json({
      success: false,
      message: error.message || 'Internal server error',
      error: error.message
    });
  }
};

/**
 * Reject product (Admin)
 */
exports.rejectProduct = async (req, res) => {
  try {
    const adminId = req.user.id;
    const { productId } = req.params;
    const { reason } = req.body;

    if (!reason || reason.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Rejection reason is required'
      });
    }

    const product = await productManagementService.rejectProduct(adminId, productId, reason);

    res.status(200).json({
      success: true,
      message: 'Product rejected successfully',
      data: product
    });
  } catch (error) {
    logger.error('Error rejecting product:', error);
    res.status(error.status || 500).json({
      success: false,
      message: error.message || 'Internal server error',
      error: error.message
    });
  }
};

// ========== INVENTORY MANAGEMENT ==========

/**
 * Get low stock products
 */
exports.getLowStockProducts = async (req, res) => {
  try {
    const sellerId = req.user.role === 'seller' ? req.user.id : null;
    
    const products = await productManagementService.getLowStockProducts(sellerId);

    res.status(200).json({
      success: true,
      message: 'Low stock products retrieved successfully',
      data: products
    });
  } catch (error) {
    logger.error('Error getting low stock products:', error);
    res.status(error.status || 500).json({
      success: false,
      message: error.message || 'Internal server error',
      error: error.message
    });
  }
};

/**
 * Update stock levels
 */
exports.updateStock = async (req, res) => {
  try {
    const sellerId = req.user.id;
    const { productId } = req.params;
    const { stock } = req.body;

    if (stock === undefined || stock < 0) {
      return res.status(400).json({
        success: false,
        message: 'Valid stock quantity is required'
      });
    }

    const product = await productManagementService.updateStock(sellerId, productId, stock);

    res.status(200).json({
      success: true,
      message: 'Stock updated successfully',
      data: product
    });
  } catch (error) {
    logger.error('Error updating stock:', error);
    res.status(error.status || 500).json({
      success: false,
      message: error.message || 'Internal server error',
      error: error.message
    });
  }
};

// ========== BULK OPERATIONS ==========

/**
 * Bulk upload products from CSV (Seller)
 */
exports.bulkUploadProducts = async (req, res) => {
  try {
    const sellerId = req.user.id;
    
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'CSV file is required'
      });
    }

    const csvFilePath = req.file.path;
    const result = await productManagementService.bulkUploadProducts(sellerId, csvFilePath);

    res.status(200).json({
      success: true,
      message: 'Bulk upload completed successfully',
      data: result
    });
  } catch (error) {
    logger.error('Error in bulk upload:', error);
    res.status(error.status || 500).json({
      success: false,
      message: error.message || 'Internal server error',
      error: error.message
    });
  }
};

/**
 * Download CSV template for bulk upload
 */
exports.downloadCSVTemplate = async (req, res) => {
  try {
    const csvTemplate = `name,description,category,price,costPrice,stock,brand,color,size,hsnCode
"Sample Electronics Product","High-quality electronics item","Electronics",10000,7000,50,"BrandName","Black","Medium","85234567"
"Sample Fashion Product","Trendy fashion item","Fashion",2000,1200,100,"FashionBrand","Red","Large","62051000"
"Sample Beauty Product","Premium beauty product","Beauty",1500,900,75,"BeautyBrand","Pink","Small","33049900"`;

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename="product_upload_template.csv"');
    res.status(200).send(csvTemplate);
  } catch (error) {
    logger.error('Error downloading CSV template:', error);
    res.status(500).json({
      success: false,
      message: 'Error downloading CSV template',
      error: error.message
    });
  }
};

// ========== PRODUCT ANALYTICS ==========

/**
 * Get product analytics for seller
 */
exports.getProductAnalytics = async (req, res) => {
  try {
    const sellerId = req.user.id;
    
    // Get product counts by status
    const analytics = await productManagementService.getSellerProducts(sellerId, { limit: 1000 });
    const products = analytics.items;

    const statusCounts = {
      total: products.length,
      approved: products.filter(p => p.approvalStatus === 'approved').length,
      pending: products.filter(p => p.approvalStatus === 'pending').length,
      rejected: products.filter(p => p.approvalStatus === 'rejected').length,
      lowStock: products.filter(p => p.stock <= p.lowStockThreshold).length,
      outOfStock: products.filter(p => p.stock === 0).length
    };

    // Category breakdown
    const categoryBreakdown = products.reduce((acc, product) => {
      acc[product.category] = (acc[product.category] || 0) + 1;
      return acc;
    }, {});

    res.status(200).json({
      success: true,
      message: 'Product analytics retrieved successfully',
      data: {
        statusCounts,
        categoryBreakdown,
        totalValue: products.reduce((sum, p) => sum + (p.price * p.stock), 0),
        avgPrice: products.length > 0 ? products.reduce((sum, p) => sum + p.price, 0) / products.length : 0
      }
    });
  } catch (error) {
    logger.error('Error getting product analytics:', error);
    res.status(error.status || 500).json({
      success: false,
      message: error.message || 'Internal server error',
      error: error.message
    });
  }
};

const userService = require('../services/user.service');

exports.getUserProfile = async (req, res) => {
  try {
    const profile = await userService.getProfile(req.user.id);
    res.json({ message: 'Profile fetched', user: profile });
  } catch (err) {
    res.status(404).json({ error: err.message });
  }
};

exports.updateUserProfile = async (req, res) => {
  try {
    const updated = await userService.updateProfile(req.user.id, req.body);
    res.json({ message: 'Profile updated', user: updated });
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

exports.uploadProfilePicture = async (req, res) => {
  try {
    if (!req.file) throw new Error('No file uploaded');
    const updated = await userService.updateProfilePicture(req.user.id, req.file);
    res.json({ message: 'Profile picture updated', user: updated });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.getWishlist = async (req, res) => {
  try {
    const wishlist = await userService.getWishlist(req.user.id);
    res.json({ message: 'Wishlist fetched', wishlist });
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

exports.addToWishlist = async (req, res) => {
  try {
    const updated = await userService.addToWishlist(req.user.id, req.body.productId);
    res.json({ message: 'Added to wishlist', user: updated });
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

exports.removeFromWishlist = async (req, res) => {
  try {
    const updated = await userService.removeFromWishlist(req.user.id, req.body.productId);
    res.json({ message: 'Removed from wishlist', user: updated });
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

exports.addToCart = async (req, res) => {
  try {
    const updated = await userService.addToCart(req.user.id, req.body.productId, req.body.quantity);
    res.json({ message: 'Added to cart', user: updated });
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

exports.removeFromCart = async (req, res) => {
  try {
    const updated = await userService.removeFromCart(req.user.id, req.body.productId);
    res.json({ message: 'Removed from cart', user: updated });
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

exports.getOrderHistory = async (req, res) => {
  try {
    const orders = await userService.getOrders(req.user.id);
    res.json({ message: 'Order history retrieved', orders });
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

exports.getOrderById = async (req, res) => {
  try {
    const order = await userService.getOrderById(req.user.id, req.params.id);
    res.json({ message: 'Order retrieved', order });
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

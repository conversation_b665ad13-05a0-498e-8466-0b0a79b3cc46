import { createRequire } from 'module';const require = createRequire(import.meta.url);
import {
  BEFORE_APP_SERIALIZED,
  DominoAdapter,
  ENABLE_DOM_EMULATION,
  INITIAL_CONFIG,
  INTERNAL_SERVER_PLATFORM_PROVIDERS,
  PlatformState,
  SERVER_CONTEXT,
  SERVER_RENDER_PROVIDERS,
  ServerModule,
  VERSION,
  platformServer,
  provideServerRendering,
  renderApplication,
  renderInternal,
  renderModule
} from "./chunk-SOVKI2NI.js";
import "./chunk-F6CDCG5Z.js";
import "./chunk-6JYC7CCA.js";
import "./chunk-TIDNVKLZ.js";
import "./chunk-WRHT6P5S.js";
import "./chunk-XZAFD5ZA.js";
import "./chunk-ZUJ64LXG.js";
import "./chunk-XCIYP5SE.js";
import "./chunk-OYTRG5F6.js";
import "./chunk-YHCV7DAQ.js";
export {
  BEFORE_APP_SERIALIZED,
  INITIAL_CONFIG,
  PlatformState,
  ServerModule,
  VERSION,
  platformServer,
  provideServerRendering,
  renderApplication,
  renderModule,
  DominoAdapter as ɵDominoAdapter,
  ENABLE_DOM_EMULATION as ɵENABLE_DOM_EMULATION,
  INTERNAL_SERVER_PLATFORM_PROVIDERS as ɵINTERNAL_SERVER_PLATFORM_PROVIDERS,
  SERVER_CONTEXT as ɵSERVER_CONTEXT,
  SERVER_RENDER_PROVIDERS as ɵSERVER_RENDER_PROVIDERS,
  renderInternal as ɵrenderInternal
};

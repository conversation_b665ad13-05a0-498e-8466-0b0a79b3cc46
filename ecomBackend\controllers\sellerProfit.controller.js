const sellerProfitService = require('../services/sellerProfit.service');
const { OrderCommission, SellerPayout } = require('../models/commission.model');
const Product = require('../models/product.model');
const mongoose = require('mongoose');

/**
 * Update product cost price
 */
exports.updateProductCostPrice = async (req, res) => {
  try {
    const { productId } = req.params;
    const { costPrice } = req.body;
    const sellerId = req.user.id;

    // Validate input
    if (!costPrice || costPrice < 0) {
      return res.status(400).json({
        success: false,
        message: 'Valid cost price is required'
      });
    }

    // Find product and verify ownership
    const product = await Product.findOne({ 
      _id: productId, 
      seller: sellerId,
      isDeleted: false 
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found or access denied'
      });
    }

    // Validate cost price is less than selling price
    if (costPrice >= product.price) {
      return res.status(400).json({
        success: false,
        message: 'Cost price must be less than selling price'
      });
    }

    // Update cost price
    product.costPrice = costPrice;
    await product.save();

    res.status(200).json({
      success: true,
      message: 'Cost price updated successfully',
      data: {
        productId: product._id,
        productName: product.name,
        costPrice: product.costPrice,
        sellingPrice: product.price,
        profitMargin: (((product.price - product.costPrice) / product.price) * 100).toFixed(2)
      }
    });
  } catch (error) {
    console.error('Error updating product cost price:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
};

/**
 * Get seller profit analytics by category
 */
exports.getSellerProfitByCategory = async (req, res) => {
  try {
    const sellerId = req.user.id;
    const { startDate, endDate } = req.query;

    const filters = {};
    if (startDate && endDate) {
      filters.startDate = startDate;
      filters.endDate = endDate;
    }

    const profitData = await sellerProfitService.getSellerProfitByCategory(sellerId, filters);

    res.status(200).json({
      success: true,
      message: 'Seller profit by category retrieved successfully',
      data: profitData,
      currency: 'INR'
    });
  } catch (error) {
    console.error('Error getting seller profit by category:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
};

/**
 * Get commission rates chart data
 */
exports.getCommissionRatesChart = async (req, res) => {
  try {
    const sellerId = req.user.id;
    
    const chartData = await sellerProfitService.getCommissionRatesForCharts(sellerId);

    res.status(200).json({
      success: true,
      message: 'Commission rates chart data retrieved successfully',
      data: chartData
    });
  } catch (error) {
    console.error('Error getting commission rates chart:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
};

/**
 * Get profit by category chart data
 */
exports.getProfitByCategoryChart = async (req, res) => {
  try {
    const sellerId = req.user.id;
    const { startDate, endDate } = req.query;

    const filters = {};
    if (startDate && endDate) {
      filters.startDate = startDate;
      filters.endDate = endDate;
    }

    const chartData = await sellerProfitService.getProfitByCategory(sellerId, filters);

    res.status(200).json({
      success: true,
      message: 'Profit by category chart data retrieved successfully',
      data: chartData,
      currency: 'INR'
    });
  } catch (error) {
    console.error('Error getting profit by category chart:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
};

/**
 * Calculate profit for a specific order (simulation)
 */
exports.calculateOrderProfit = async (req, res) => {
  try {
    const { productId, quantity = 1 } = req.body;
    const sellerId = req.user.id;

    if (!productId) {
      return res.status(400).json({
        success: false,
        message: 'Product ID is required'
      });
    }

    const orderData = {
      orderId: new mongoose.Types.ObjectId(), // Simulated order ID
      sellerId,
      productId,
      quantity
    };

    const profitCalculation = await sellerProfitService.calculateOrderProfit(orderData);

    res.status(200).json({
      success: true,
      message: 'Order profit calculated successfully',
      data: profitCalculation
    });
  } catch (error) {
    console.error('Error calculating order profit:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
};

/**
 * Get seller dashboard summary
 */
exports.getSellerDashboardSummary = async (req, res) => {
  try {
    const sellerId = req.user.id;
    const { startDate, endDate } = req.query;

    const matchStage = {
      sellerId: new mongoose.Types.ObjectId(sellerId)
    };

    if (startDate && endDate) {
      matchStage.calculatedAt = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }

    // Get overall summary
    const summary = await OrderCommission.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: null,
          totalOrders: { $sum: 1 },
          totalRevenue: { $sum: '$sellingPriceExcludingGst' },
          totalCommission: { $sum: '$commissionAmount' },
          totalDeliveryFees: { $sum: '$deliveryFee' },
          totalProductGst: { $sum: '$productGstAmount' },
          totalProfit: { $sum: '$netSellerEarnings' },
          avgCommissionRate: { $avg: '$commissionRate' }
        }
      },
      {
        $project: {
          _id: 0,
          totalOrders: 1,
          totalRevenue: { $round: ['$totalRevenue', 2] },
          totalCommission: { $round: ['$totalCommission', 2] },
          totalDeliveryFees: { $round: ['$totalDeliveryFees', 2] },
          totalProductGst: { $round: ['$totalProductGst', 2] },
          totalProfit: { $round: ['$totalProfit', 2] },
          avgCommissionRate: { $round: ['$avgCommissionRate', 2] },
          profitMargin: {
            $round: [
              {
                $multiply: [
                  { $divide: ['$totalProfit', '$totalRevenue'] },
                  100
                ]
              },
              2
            ]
          }
        }
      }
    ]);

    // Get pending payouts
    const pendingPayouts = await SellerPayout.find({
      sellerId: new mongoose.Types.ObjectId(sellerId),
      status: { $in: ['pending', 'processing'] }
    }).sort({ createdAt: -1 });

    const dashboardData = {
      summary: summary[0] || {
        totalOrders: 0,
        totalRevenue: 0,
        totalCommission: 0,
        totalDeliveryFees: 0,
        totalProductGst: 0,
        totalProfit: 0,
        avgCommissionRate: 0,
        profitMargin: 0
      },
      pendingPayouts,
      currency: 'INR'
    };

    res.status(200).json({
      success: true,
      message: 'Seller dashboard summary retrieved successfully',
      data: dashboardData
    });
  } catch (error) {
    console.error('Error getting seller dashboard summary:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
};

/**
 * Get seller payout history
 */
exports.getSellerPayoutHistory = async (req, res) => {
  try {
    const sellerId = req.user.id;
    const { page = 1, limit = 10, status } = req.query;

    const query = { sellerId: new mongoose.Types.ObjectId(sellerId) };
    if (status) {
      query.status = status;
    }

    const payouts = await SellerPayout.find(query)
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .populate('orderIds', 'orderNumber totalAmount');

    const total = await SellerPayout.countDocuments(query);

    res.status(200).json({
      success: true,
      message: 'Seller payout history retrieved successfully',
      data: {
        payouts,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(total / limit),
          totalItems: total,
          itemsPerPage: parseInt(limit)
        }
      },
      currency: 'INR'
    });
  } catch (error) {
    console.error('Error getting seller payout history:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
};

/**
 * Get GST report for seller
 */
exports.getSellerGstReport = async (req, res) => {
  try {
    const sellerId = req.user.id;
    const { startDate, endDate, month, year } = req.query;

    let matchStage = {
      sellerId: new mongoose.Types.ObjectId(sellerId)
    };

    // Date filtering
    if (startDate && endDate) {
      matchStage.calculatedAt = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    } else if (month && year) {
      const startOfMonth = new Date(year, month - 1, 1);
      const endOfMonth = new Date(year, month, 0, 23, 59, 59);
      matchStage.calculatedAt = {
        $gte: startOfMonth,
        $lte: endOfMonth
      };
    }

    const gstReport = await OrderCommission.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: null,
          totalOrders: { $sum: 1 },
          totalProductGst: { $sum: '$productGstAmount' },
          totalCommissionGst: { $sum: '$commissionGstAmount' },
          totalDeliveryGst: { $sum: '$deliveryGstAmount' },
          totalGstCollectedFromCustomer: { $sum: '$gstCollectedFromCustomer' },
          totalGstCollectedFromSeller: { $sum: '$gstCollectedFromSeller' },
          totalGstToRemit: { $sum: '$totalGstToRemit' }
        }
      },
      {
        $project: {
          _id: 0,
          totalOrders: 1,
          totalProductGst: { $round: ['$totalProductGst', 2] },
          totalCommissionGst: { $round: ['$totalCommissionGst', 2] },
          totalDeliveryGst: { $round: ['$totalDeliveryGst', 2] },
          totalGstCollectedFromCustomer: { $round: ['$totalGstCollectedFromCustomer', 2] },
          totalGstCollectedFromSeller: { $round: ['$totalGstCollectedFromSeller', 2] },
          totalGstToRemit: { $round: ['$totalGstToRemit', 2] }
        }
      }
    ]);

    res.status(200).json({
      success: true,
      message: 'Seller GST report retrieved successfully',
      data: gstReport[0] || {
        totalOrders: 0,
        totalProductGst: 0,
        totalCommissionGst: 0,
        totalDeliveryGst: 0,
        totalGstCollectedFromCustomer: 0,
        totalGstCollectedFromSeller: 0,
        totalGstToRemit: 0
      },
      currency: 'INR'
    });
  } catch (error) {
    console.error('Error getting seller GST report:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
};

import { Component, OnInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { NavbarComponent } from './page/navbar/navbar.component';
import { FooterComponent } from './page/footer/footer.component';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { SharedModule } from './shared/shared.module';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    RouterOutlet,
    NavbarComponent,
    FooterComponent,
    FontAwesomeModule,
    SharedModule,
    CommonModule
  ],
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css']
})
export class AppComponent implements OnInit {
  title = 'BuyFusion';
  isLoading = true;
  hasError = false;

  ngOnInit(): void {
    // Simulate app initialization
    setTimeout(() => {
      try {
        // App initialization logic here
        this.isLoading = false;
        console.log('BuyFusion app loaded successfully!');
      } catch (error) {
        console.error('App initialization error:', error);
        this.isLoading = false;
        this.hasError = true;
      }
    }, 2000); // Show loading for 2 seconds
  }

  reload(): void {
    window.location.reload();
  }
}
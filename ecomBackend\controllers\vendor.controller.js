const Vendor = require('../models/vendor.model');
const User = require('../models/user.model');
const Product = require('../models/product.model');
const Order = require('../models/order.model');
const mongoose = require('mongoose');

// Register as vendor
exports.registerVendor = async (req, res) => {
  try {
    const {
      businessName,
      businessType,
      gstin,
      panNumber,
      businessAddress,
      contactPerson,
      bankDetails
    } = req.body;

    // Check if user already has a vendor profile
    const existingVendor = await Vendor.findOne({ userId: req.user.id });
    if (existingVendor) {
      return res.status(400).json({ error: 'Vendor profile already exists' });
    }

    // Check if GSTIN is already registered
    const existingGstin = await Vendor.findOne({ gstin });
    if (existingGstin) {
      return res.status(400).json({ error: 'GSTIN already registered' });
    }

    const vendor = new Vendor({
      userId: req.user.id,
      businessName,
      businessType,
      gstin,
      panNumber,
      businessAddress,
      contact<PERSON>erson,
      bankDetails,
      approvalStatus: 'pending'
    });

    await vendor.save();

    // Update user role to vendor
    await User.findByIdAndUpdate(req.user.id, { role: 'vendor' });

    res.status(201).json({
      message: 'Vendor registration submitted successfully',
      vendor: {
        id: vendor._id,
        businessName: vendor.businessName,
        approvalStatus: vendor.approvalStatus
      }
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Get vendor status
exports.getVendorStatus = async (req, res) => {
  try {
    const vendor = await Vendor.findOne({ userId: req.user.id });
    if (!vendor) {
      return res.status(404).json({ error: 'Vendor profile not found' });
    }

    res.json({
      approvalStatus: vendor.approvalStatus,
      businessName: vendor.businessName,
      approvedAt: vendor.approvedAt,
      rejectionReason: vendor.rejectionReason
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Get vendor profile
exports.getVendorProfile = async (req, res) => {
  try {
    const vendor = await Vendor.findOne({ userId: req.user.id }).populate('userId', 'name email');
    if (!vendor) {
      return res.status(404).json({ error: 'Vendor profile not found' });
    }

    res.json({ vendor });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Update vendor profile
exports.updateVendorProfile = async (req, res) => {
  try {
    const vendor = await Vendor.findOneAndUpdate(
      { userId: req.user.id },
      req.body,
      { new: true, runValidators: true }
    );

    if (!vendor) {
      return res.status(404).json({ error: 'Vendor profile not found' });
    }

    res.json({ message: 'Profile updated successfully', vendor });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Upload documents
exports.uploadDocuments = async (req, res) => {
  try {
    const vendor = await Vendor.findOne({ userId: req.user.id });
    if (!vendor) {
      return res.status(404).json({ error: 'Vendor profile not found' });
    }

    // Update documents from uploaded files
    if (req.files) {
      const documents = {};
      Object.keys(req.files).forEach(key => {
        if (req.files[key][0]) {
          documents[key] = {
            public_id: req.files[key][0].filename,
            url: req.files[key][0].path
          };
        }
      });

      vendor.documents = { ...vendor.documents, ...documents };
      await vendor.save();
    }

    res.json({ message: 'Documents uploaded successfully', documents: vendor.documents });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Add product
exports.addProduct = async (req, res) => {
  try {
    const vendor = await Vendor.findOne({ userId: req.user.id });
    if (!vendor || vendor.approvalStatus !== 'approved') {
      return res.status(403).json({ error: 'Vendor not approved' });
    }

    const productData = {
      ...req.body,
      sellerId: req.user.id,
      vendorId: vendor._id
    };

    const product = new Product(productData);
    await product.save();

    res.status(201).json({ message: 'Product added successfully', product });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Get vendor products
exports.getVendorProducts = async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const vendor = await Vendor.findOne({ userId: req.user.id });
    
    if (!vendor) {
      return res.status(404).json({ error: 'Vendor profile not found' });
    }

    const products = await Product.find({ sellerId: req.user.id })
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });

    const total = await Product.countDocuments({ sellerId: req.user.id });

    res.json({
      products,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Update product
exports.updateProduct = async (req, res) => {
  try {
    const { productId } = req.params;
    const product = await Product.findOneAndUpdate(
      { _id: productId, sellerId: req.user.id },
      req.body,
      { new: true, runValidators: true }
    );

    if (!product) {
      return res.status(404).json({ error: 'Product not found' });
    }

    res.json({ message: 'Product updated successfully', product });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Delete product
exports.deleteProduct = async (req, res) => {
  try {
    const { productId } = req.params;
    const product = await Product.findOneAndDelete({
      _id: productId,
      sellerId: req.user.id
    });

    if (!product) {
      return res.status(404).json({ error: 'Product not found' });
    }

    res.json({ message: 'Product deleted successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Bulk upload products
exports.bulkUploadProducts = async (req, res) => {
  try {
    const { products } = req.body;
    const vendor = await Vendor.findOne({ userId: req.user.id });
    
    if (!vendor || vendor.approvalStatus !== 'approved') {
      return res.status(403).json({ error: 'Vendor not approved' });
    }

    const productsWithVendor = products.map(product => ({
      ...product,
      sellerId: req.user.id,
      vendorId: vendor._id
    }));

    const result = await Product.insertMany(productsWithVendor);
    res.status(201).json({ 
      message: `${result.length} products uploaded successfully`,
      products: result
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Get vendor orders
exports.getVendorOrders = async (req, res) => {
  try {
    const { page = 1, limit = 10, status } = req.query;
    const query = { 'products.sellerId': req.user.id };
    
    if (status) {
      query.status = status;
    }

    const orders = await Order.find(query)
      .populate('userId', 'name email')
      .populate('products.productId', 'name price')
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });

    const total = await Order.countDocuments(query);

    res.json({
      orders,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Update order status
exports.updateOrderStatus = async (req, res) => {
  try {
    const { orderId } = req.params;
    const { status } = req.body;

    const order = await Order.findOneAndUpdate(
      { 
        _id: orderId,
        'products.sellerId': req.user.id
      },
      { status },
      { new: true }
    );

    if (!order) {
      return res.status(404).json({ error: 'Order not found' });
    }

    res.json({ message: 'Order status updated successfully', order });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Get inventory
exports.getInventory = async (req, res) => {
  try {
    const products = await Product.find({ sellerId: req.user.id })
      .select('name price stock category')
      .sort({ stock: 1 });

    res.json({ inventory: products });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Update stock
exports.updateStock = async (req, res) => {
  try {
    const { productId } = req.params;
    const { stock } = req.body;

    const product = await Product.findOneAndUpdate(
      { _id: productId, sellerId: req.user.id },
      { stock },
      { new: true }
    );

    if (!product) {
      return res.status(404).json({ error: 'Product not found' });
    }

    res.json({ message: 'Stock updated successfully', product });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Get low stock alerts
exports.getLowStockAlerts = async (req, res) => {
  try {
    const lowStockProducts = await Product.find({
      sellerId: req.user.id,
      stock: { $lt: 10 }
    }).select('name stock price');

    res.json({ 
      message: `${lowStockProducts.length} products with low stock`,
      products: lowStockProducts 
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Get analytics
exports.getAnalytics = async (req, res) => {
  try {
    const totalProducts = await Product.countDocuments({ sellerId: req.user.id });
    const totalOrders = await Order.countDocuments({ 'products.sellerId': req.user.id });
    
    // Get sales data
    const salesData = await Order.aggregate([
      { $match: { 'products.sellerId': new mongoose.Types.ObjectId(req.user.id) } },
      { $group: { _id: null, totalSales: { $sum: '$totalAmount' } } }
    ]);

    const totalSales = salesData.length > 0 ? salesData[0].totalSales : 0;

    res.json({
      analytics: {
        totalProducts,
        totalOrders,
        totalSales,
        lowStockCount: await Product.countDocuments({ 
          sellerId: req.user.id, 
          stock: { $lt: 10 } 
        })
      }
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};
